from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_instance_deletion_protection": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_DELETION_PROTECTION.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            instances = (cached.get("instances") or {}).get("DBInstances", [])

            for instance in instances:
                db_instance_id = instance["DBInstanceIdentifier"]
                deletion_protection = instance.get("DeletionProtection", False)

                if (findings["rds_instance_deletion_protection"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and not deletion_protection):
                    findings["rds_instance_deletion_protection"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["rds_instance_deletion_protection"]["details"].append({
                    "db_instance_id": db_instance_id,
                    "region": region,
                    "compliance": deletion_protection
                })

        return findings

    def remediate(self):
        pass
