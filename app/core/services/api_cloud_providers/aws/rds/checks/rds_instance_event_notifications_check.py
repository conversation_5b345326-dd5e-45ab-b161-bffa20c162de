from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_instance_event_notifications": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_INSTANCE_EVENT_NOTIFICATIONS.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            event_subscriptions = (cached.get("event_subscriptions") or {}).get("EventSubscriptionsList", [])

            critical_events = {"failover", "maintenance", "configuration change", "deletion"}
            has_critical_subscription = False

            for subscription in event_subscriptions:
                source_type = subscription.get("SourceType", "")
                event_categories = set(subscription.get("EventCategoriesList", []))

                if source_type == "db-instance" and critical_events.intersection(event_categories):
                    has_critical_subscription = True
                    break

            if (findings["rds_instance_event_notifications"]["status"] == ResourceComplianceStatusEnum.PASS.value
                    and not has_critical_subscription):
                findings["rds_instance_event_notifications"]["status"] = ResourceComplianceStatusEnum.FAIL.value

            findings["rds_instance_event_notifications"]["details"].append({
                "region": region,
                "compliance": has_critical_subscription
            })

        return findings

    def remediate(self):
        pass
