from app import app
from app.core.models.mysql import get_user_data
from app.common import (UserExistsException, InternalServerException, WorkspaceNameRequiredException, EmailSendException, OTPResendCooldownException)
from app.core.services.email_verification_service import EmailVerificationService
from app.core.services.celery_conf.tasks import send_verification_email_task
import logging

__all__ = ['SignUpService']

logger = logging.getLogger(__name__)


class SignUpService:
    """
    SignUpService that sends verification link for email verification instead of immediately creating user
    """
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool
        self.verification_service = EmailVerificationService()

    async def process(self):
        """
        Process signup request by sending verification link email
        """
        # Check if user already exists in users table (fully verified users)
        user = await get_user_data(self.conn_pool, self.message)
        if user and user.get("email") and user["email"].lower() == self.message.email.lower():
            raise UserExistsException("User with this email already exists")

        # Validate required fields
        if not self.message.workspace_name:
            raise WorkspaceNameRequiredException("Workspace name is required")

        try:
            # Prepare signup data to store with verification token
            signup_data = {
                "email": self.message.email,
                "password": self.message.password,  # Will be encrypted in verification service
                "workspace_name": self.message.workspace_name,
                "first_name": self.message.first_name,
                "last_name": self.message.last_name
            }

            # Create and store verification token
            verification_token = await self.verification_service.create_and_store_verification_token(
                email=self.message.email,
                signup_data=signup_data
            )

            # Generate verification URL
            verification_url = self.verification_service.generate_verification_url(verification_token)

            # Send verification email asynchronously using Celery
            send_verification_email_task.delay(
                recipient_email=self.message.email,
                verification_url=verification_url,
                first_name=self.message.first_name
            )

            logger.info(f"Verification link sent for signup: {self.message.email}")

            return {
                "message": "Verification link sent to your email",
                "email": self.message.email,
                "expires_in_minutes": app.config.OTP_EXPIRY_MINUTES
            }

        except OTPResendCooldownException as e:
            # Handle case where user tries to signup again too quickly
            logger.info(f"Signup attempted too quickly for existing verification: {self.message.email}")
            # Return success message to prevent enumeration, but inform about existing verification
            return {
                "message": "If you haven't received the verification email, please check your spam folder or wait a few minutes before trying again.",
                "email": self.message.email,
                "expires_in_minutes": app.config.OTP_EXPIRY_MINUTES,
                "note": "verification_pending"
            }

        except EmailSendException as e:
            logger.error(f"Failed to send verification email for signup: {str(e)}")
            raise InternalServerException("Failed to send verification email. Please try again.")

        except Exception as e:
            logger.error(f"Signup process failed: {str(e)}")
            raise InternalServerException("Signup process failed. Please try again.")
