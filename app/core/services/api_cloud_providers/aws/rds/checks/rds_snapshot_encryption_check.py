from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_snapshot_encryption": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_SNAPSHOT_ENCRYPTION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            db_snapshots = (cached.get("db_snapshots") or {}).get("DBSnapshots", [])
            cluster_snapshots = (cached.get("cluster_snapshots") or {}).get("DBClusterSnapshots", [])

            # Check individual RDS DB instance snapshots
            for snapshot in db_snapshots:
                snapshot_id = snapshot["DBSnapshotIdentifier"]
                db_instance_id = snapshot["DBInstanceIdentifier"]
                encrypted = snapshot.get("Encrypted", False)

                if (findings["rds_snapshot_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and not encrypted):
                    findings["rds_snapshot_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["rds_snapshot_encryption"]["details"].append({
                    "snapshot_id": snapshot_id,
                    "db_instance_id": db_instance_id,
                    "region": region,
                    "compliance": encrypted
                })

            # Check Aurora DB cluster snapshots
            for snapshot in cluster_snapshots:
                snapshot_id = snapshot["DBClusterSnapshotIdentifier"]
                cluster_id = snapshot["DBClusterIdentifier"]
                encrypted = snapshot.get("Encrypted", False)

                if (findings["rds_snapshot_encryption"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and not encrypted):
                    findings["rds_snapshot_encryption"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["rds_snapshot_encryption"]["details"].append({
                    "snapshot_id": snapshot_id,
                    "db_cluster_id": cluster_id,
                    "region": region,
                    "compliance": encrypted
                })

        return findings

    def remediate(self):
        pass
