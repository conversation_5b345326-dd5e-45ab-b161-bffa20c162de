import asyncio
import json
import os
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor


CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "eks")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"eks_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "eks")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of EKS-related resources that checks can consume.
    """

    if not isinstance(region, str) or not region or region.lower() == 'global' or '-' not in region:
        raise ValueError(f"Invalid region passed to EKS fetch: {region!r}")

    async with session.client(AWSServiceNameEnum.EKS.value, region_name=region) as client:
        # Get supported Kubernetes versions for this region
        supported_versions = {}
        try:
            cluster_versions_response = await client.describe_cluster_versions()
            supported_versions = cluster_versions_response
        except Exception:
            # If API call fails, fall back to empty dict
            supported_versions = {"clusterVersions": []}

        # Get all clusters in the region
        clusters_response = await client.list_clusters()
        clusters = clusters_response.get("clusters", [])

        # Get detailed information for each cluster
        cluster_details = {}
        for cluster_arn in clusters:
            cluster_name = cluster_arn.split("/")[-1]
            try:
                cluster_detail = await client.describe_cluster(name=cluster_name)
                cluster_details[cluster_name] = cluster_detail
            except Exception:
                # Cluster may not be accessible or may have been deleted
                cluster_details[cluster_name] = {"cluster": {}}
        
        # Get node groups for each cluster
        node_groups = {}
        node_group_details = {}
        for cluster_arn in clusters:
            cluster_name = cluster_arn.split("/")[-1]
            try:
                node_groups_response = await client.list_nodegroups(clusterName=cluster_name)
                cluster_node_groups = node_groups_response.get("nodegroups", [])
                node_groups[cluster_name] = cluster_node_groups
                
                # Get detailed information for each node group
                node_group_details[cluster_name] = {}
                for node_group_name in cluster_node_groups:
                    try:
                        node_group_detail = await client.describe_nodegroup(
                            clusterName=cluster_name,
                            nodegroupName=node_group_name
                        )
                        node_group_details[cluster_name][node_group_name] = node_group_detail
                    except Exception:
                        # Node group may not be accessible
                        node_group_details[cluster_name][node_group_name] = {"nodegroup": {}}
            except Exception:
                # Cluster may not support node groups or may not be accessible
                node_groups[cluster_name] = []
                node_group_details[cluster_name] = {}
        
        # Get add-ons for each cluster
        addons = {}
        addon_details = {}
        for cluster_arn in clusters:
            cluster_name = cluster_arn.split("/")[-1]
            try:
                addons_response = await client.list_addons(clusterName=cluster_name)
                cluster_addons = addons_response.get("addons", [])
                addons[cluster_name] = cluster_addons
                
                # Get detailed information for each add-on
                addon_details[cluster_name] = {}
                for addon_name in cluster_addons:
                    try:
                        addon_detail = await client.describe_addon(
                            clusterName=cluster_name,
                            addonName=addon_name
                        )
                        addon_details[cluster_name][addon_name] = addon_detail
                    except Exception:
                        # Add-on may not be accessible
                        addon_details[cluster_name][addon_name] = {"addon": {}}
            except Exception:
                # Cluster may not support add-ons or may not be accessible
                addons[cluster_name] = []
                addon_details[cluster_name] = {}

    return {
        "region": region,
        "clusters": clusters_response,
        "cluster_details": cluster_details,
        "node_groups": node_groups,
        "node_group_details": node_group_details,
        "addons": addons,
        "addon_details": addon_details,
        "supported_versions": supported_versions,
    }


async def fetch_and_cache_eks_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch EKS resources for a region and cache to JSON file `eks_<region>.json`.
    Returns the absolute file path for the region cache.
    """
    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path
    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_eks_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    """
    # Filter regions: drop invalid/falsy and 'global'
    regions = [r for r in regions if isinstance(r, str) and r and r.lower() != 'global']
    if not regions:
        return []
    tasks = [fetch_and_cache_eks_region_data(region, session_factory, workspace_id, aws_account_id) for region in regions]
    return await asyncio.gather(*tasks)


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


def get_supported_kubernetes_versions(cached_data: Dict[str, Any]) -> List[str]:
    """
    Extract supported Kubernetes versions from cached EKS data.
    Returns a list of supported version strings (e.g., ["1.31", "1.32", "1.33"]).

    Args:
        cached_data: The cached region data containing supported_versions

    Returns:
        List of supported Kubernetes version strings
    """
    supported_versions_data = cached_data.get("supported_versions", {})
    cluster_versions = supported_versions_data.get("clusterVersions", [])

    # Extract version strings from the cluster versions data
    # Filter to only include versions in STANDARD_SUPPORT status
    supported_versions = []
    for version_info in cluster_versions:
        version = version_info.get("clusterVersion")
        status = version_info.get("status", "")

        # Include both STANDARD_SUPPORT and EXTENDED_SUPPORT versions as "supported"
        # but prioritize STANDARD_SUPPORT versions
        if version and status in ["STANDARD_SUPPORT", "EXTENDED_SUPPORT"]:
            supported_versions.append(version)

    # If no versions found from API, fall back to a reasonable default
    if not supported_versions:
        # These are current versions as of late 2024/early 2025
        supported_versions = ["1.31", "1.32", "1.33"]

    return supported_versions


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Generic wrapper function that calls the EKS-specific caching function.
    This maintains consistency with other service implementations.
    """
    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id
    
    return await fetch_and_cache_eks_all_regions(regions, session_factory, workspace_id, aws_account_id)
