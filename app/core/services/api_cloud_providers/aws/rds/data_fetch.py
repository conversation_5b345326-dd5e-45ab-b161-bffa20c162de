import asyncio
import json
import os
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor


CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "rds")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"rds_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "rds")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of RDS-related resources that checks can consume.
    """

    if not isinstance(region, str) or not region or region.lower() == 'global' or '-' not in region:
        raise ValueError(f"Invalid region passed to RDS fetch: {region!r}")
    
    async with session.client(AWSServiceNameEnum.RDS.value, region_name=region) as client:
        # Core RDS resources
        instances = await client.describe_db_instances()
        clusters = await client.describe_db_clusters()
        db_snapshots = await client.describe_db_snapshots()
        cluster_snapshots = await client.describe_db_cluster_snapshots()
        
        # Parameter groups and configurations
        db_parameter_groups = await client.describe_db_parameter_groups()
        db_cluster_parameter_groups = await client.describe_db_cluster_parameter_groups()
        
        # Subnet groups and networking
        db_subnet_groups = await client.describe_db_subnet_groups()
        
        # Event subscriptions and notifications
        event_subscriptions = await client.describe_event_subscriptions()
        
        # SSL certificates
        certificates = await client.describe_certificates()
        
        # Engine versions for validation
        engine_versions = {}
        supported_engines = ["mysql", "mariadb", "postgres", "aurora-mysql", "aurora-postgresql"]
        for engine in supported_engines:
            try:
                engine_versions[engine] = await client.describe_db_engine_versions(
                    Engine=engine,
                    IncludeAll=True
                )
            except Exception:
                # Some engines might not be available in all regions
                engine_versions[engine] = {"DBEngineVersions": []}
        
        # Get parameter details for parameter groups
        parameter_group_details = {}
        for pg in db_parameter_groups.get("DBParameterGroups", []):
            pg_name = pg["DBParameterGroupName"]
            try:
                parameter_group_details[pg_name] = await client.describe_db_parameters(
                    DBParameterGroupName=pg_name
                )
            except Exception:
                # Parameter group may not be accessible
                parameter_group_details[pg_name] = {"Parameters": []}
        
        # Get cluster parameter details
        cluster_parameter_group_details = {}
        for cpg in db_cluster_parameter_groups.get("DBClusterParameterGroups", []):
            cpg_name = cpg["DBClusterParameterGroupName"]
            try:
                cluster_parameter_group_details[cpg_name] = await client.describe_db_cluster_parameters(
                    DBClusterParameterGroupName=cpg_name
                )
            except Exception:
                # Parameter group may not be accessible
                cluster_parameter_group_details[cpg_name] = {"Parameters": []}
        
        # Get snapshot attributes for privacy checks
        snapshot_attributes = {}
        for snapshot in db_snapshots.get("DBSnapshots", []):
            snapshot_id = snapshot["DBSnapshotIdentifier"]
            try:
                attr_response = await client.describe_db_snapshot_attributes(
                    DBSnapshotIdentifier=snapshot_id
                )
                snapshot_attributes[snapshot_id] = attr_response
            except Exception:
                # Some snapshots may not be accessible
                snapshot_attributes[snapshot_id] = {"DBSnapshotAttributesResult": {"DBSnapshotAttributes": []}}

    # Get AWS Backup information
    backup_plans = {}
    try:
        async with session.client('backup', region_name=region) as backup_client:
            backup_plans_response = await backup_client.list_backup_plans()
            backup_plans = backup_plans_response
            
            # Get detailed backup plan information
            backup_plan_details = {}
            for plan in backup_plans.get("BackupPlansList", []):
                plan_id = plan["BackupPlanId"]
                try:
                    plan_details = await backup_client.get_backup_plan(BackupPlanId=plan_id)
                    backup_plan_details[plan_id] = plan_details
                except Exception:
                    backup_plan_details[plan_id] = {}
            
            backup_plans["backup_plan_details"] = backup_plan_details
    except Exception:
        # Backup service might not be available
        backup_plans = {"BackupPlansList": [], "backup_plan_details": {}}

    return {
        "region": region,
        "instances": instances,
        "clusters": clusters,
        "db_snapshots": db_snapshots,
        "cluster_snapshots": cluster_snapshots,
        "db_parameter_groups": db_parameter_groups,
        "db_cluster_parameter_groups": db_cluster_parameter_groups,
        "db_subnet_groups": db_subnet_groups,
        "event_subscriptions": event_subscriptions,
        "certificates": certificates,
        "engine_versions": engine_versions,
        "parameter_group_details": parameter_group_details,
        "cluster_parameter_group_details": cluster_parameter_group_details,
        "snapshot_attributes": snapshot_attributes,
        "backup_plans": backup_plans,
    }


async def fetch_and_cache_rds_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch RDS resources for a region and cache to JSON file `rds_<region>.json`.
    Returns the absolute file path for the region cache.
    """
    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path
    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_rds_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    """
    # Filter regions: drop invalid/falsy and 'global'
    regions = [r for r in regions if isinstance(r, str) and r and r.lower() != 'global']
    if not regions:
        return []
    tasks = [fetch_and_cache_rds_region_data(region, session_factory, workspace_id, aws_account_id) for region in regions]
    return await asyncio.gather(*tasks)


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Generic wrapper function that calls the RDS-specific caching function.
    This maintains consistency with other service implementations.
    """
    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id
    
    return await fetch_and_cache_rds_all_regions(regions, session_factory, workspace_id, aws_account_id)
