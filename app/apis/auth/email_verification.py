from fastapi import Request, Response
from fastapi.responses import RedirectResponse
from app import router, app
from app.common import (
    OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException,
    OTPResendCooldownException, DefaultResponseSchema
)
# Note: We reuse OTP exception names for email verification for consistency
from app.core.services.auth import EmailVerificationAuthService, ResendVerificationLinkService

__all__ = ['verify_email', 'resend_verification_link']


@router.get("/verify-email")
async def verify_email(request: Request, response: Response, token: str) -> RedirectResponse:
    """
    Verify email using verification token from email link.
    Redirects users to the frontend with appropriate query parameters.

    This endpoint is called when users click the verification link in their email.
    It validates the token and creates the user account if the token is valid,
    then redirects to the frontend application.

    Args:
        token: Verification token from the email link

    Returns:
        RedirectResponse: Redirect to frontend with status parameters
    """
    frontend_url = app.config.FRONTEND_URL

    try:
        # Create a message object with the token
        message = type('obj', (object,), {'token': token})()

        service = EmailVerificationAuthService(message)
        result = await service.process()

        # Get user email from result for the redirect
        user_email = result.get('email', '')

        # Redirect to email verification success page with token and email parameters
        return RedirectResponse(
            url=f"{frontend_url}/email-verification-success?token={token}&email={user_email}&verified=true",
            status_code=302
        )

    except OTPExpiredException:
        # Token has expired - redirect to verification pending page with error
        return RedirectResponse(
            url=f"{frontend_url}/verification-pending?error=expired&token={token}",
            status_code=302
        )

    except OTPNotFoundException:
        # Invalid token - redirect to verification pending page with error
        return RedirectResponse(
            url=f"{frontend_url}/verification-pending?error=invalid&token={token}",
            status_code=302
        )

    except OTPAlreadyVerifiedException:
        # Token already used - redirect to success page with already verified parameter
        return RedirectResponse(
            url=f"{frontend_url}/email-verification-success?error=already_verified&token={token}",
            status_code=302
        )

    except Exception as e:
        # Generic error - redirect to verification pending page with generic error
        return RedirectResponse(
            url=f"{frontend_url}/verification-pending?error=verification_failed&token={token}",
            status_code=302
        )


@router.post("/resend-verification-link", response_model=DefaultResponseSchema)
async def resend_verification_link(request: Request, response: Response, payload: dict):
    """
    Resend verification link to user's email
    
    This endpoint allows users to request a new verification link if the previous one
    has expired or was not received. Rate limiting is applied to prevent abuse.
    
    Expected payload: {"email": "<EMAIL>"}
    """
    try:
        # Extract email from payload
        email = payload.get('email')
        if not email:
            response.status_code = 400
            return DefaultResponseSchema(
                ok=False,
                status_code=400,
                message="Email address is required"
            )
        
        # Create a message object with the email
        message = type('obj', (object,), {'email': email})()
        
        service = ResendVerificationLinkService(message)
        result = await service.process()

        app.logger.info(f"✅ Resend verification link API successful for: {email}")

        return DefaultResponseSchema(
            ok=True,
            status_code=200,
            message=result["message"],
            data={
                "email": result["email"],
                "expires_in_minutes": result["expires_in_minutes"]
            }
        )
    
    except OTPResendCooldownException as e:
        app.logger.info(f"🚨 Resend verification cooldown triggered for: {payload.get('email', 'unknown')}")
        response.status_code = 429
        return DefaultResponseSchema(
            ok=False,
            status_code=429,
            message=str(e),
            data={"error_type": "resend_cooldown"}
        )
    
    except OTPNotFoundException:
        response.status_code = 404
        return DefaultResponseSchema(
            ok=False,
            status_code=404,
            message="No pending verification found for this email.",
            data={"error_type": "no_pending_verification"}
        )
    
    except Exception as e:
        response.status_code = 500
        return DefaultResponseSchema(
            ok=False,
            status_code=500,
            message="Failed to resend verification link. Please try again.",
            data={"error_type": "internal_error"}
        )



