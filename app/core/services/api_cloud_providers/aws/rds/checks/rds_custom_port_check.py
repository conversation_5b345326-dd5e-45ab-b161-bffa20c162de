from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_custom_port": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_CUSTOM_PORT.value,
                "severity": SeverityEnum.LOW.value,
            }
        }

        # Default ports for different database engines
        default_ports = {
            "mysql": 3306,
            "mariadb": 3306,
            "postgres": 5432,
            "oracle-ee": 1521,
            "oracle-se2": 1521,
            "oracle-se1": 1521,
            "oracle-se": 1521,
            "sqlserver-ee": 1433,
            "sqlserver-se": 1433,
            "sqlserver-ex": 1433,
            "sqlserver-web": 1433,
            "aurora-mysql": 3306,
            "aurora-postgresql": 5432,
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            instances = (cached.get("instances") or {}).get("DBInstances", [])

            for instance in instances:
                db_instance_id = instance["DBInstanceIdentifier"]
                engine = instance["Engine"]
                port = instance.get("DbInstancePort", 0)

                expected_default_port = default_ports.get(engine, 0)
                using_custom_port = port != expected_default_port

                if using_custom_port:
                    findings["rds_custom_port"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["rds_custom_port"]["details"].append({
                    "db_instance_id": db_instance_id,
                    "engine": engine,
                    "region": region,
                    "compliance": using_custom_port
                })

        return findings

    def remediate(self):
        pass
