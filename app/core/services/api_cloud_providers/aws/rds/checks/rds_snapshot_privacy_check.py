from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_snapshot_privacy": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_SNAPSHOT_PRIVACY.value,
                "severity": SeverityEnum.CRITICAL.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            db_snapshots = (cached.get("db_snapshots") or {}).get("DBSnapshots", [])
            snapshot_attributes = cached.get("snapshot_attributes", {})

            for snapshot in db_snapshots:
                snapshot_id = snapshot["DBSnapshotIdentifier"]
                engine = snapshot["Engine"]

                attributes = snapshot_attributes.get(snapshot_id, {}).get("DBSnapshotAttributesResult", {}).get("DBSnapshotAttributes", [])
                public_snapshot = any(attr["AttributeValues"] for attr in attributes
                                      if attr["AttributeName"] == "restore")

                if (findings["rds_snapshot_privacy"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and public_snapshot):
                    findings["rds_snapshot_privacy"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["rds_snapshot_privacy"]["details"].append({
                    "snapshot_id": snapshot_id,
                    "engine": engine,
                    "region": region,
                    "compliance": not public_snapshot
                })

        return findings

    def remediate(self):
        pass
