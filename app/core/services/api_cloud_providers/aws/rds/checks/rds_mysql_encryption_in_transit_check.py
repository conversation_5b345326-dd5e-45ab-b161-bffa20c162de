from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, RDSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.rds.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "rds_mysql_encryption_in_transit": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": RDSChecksDescriptionEnum.RDS_MYSQL_ENCRYPTION_IN_TRANSIT.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            instances = (cached.get("instances") or {}).get("DBInstances", [])
            parameter_group_details = cached.get("parameter_group_details", {})

            for instance in instances:
                db_instance_id = instance["DBInstanceIdentifier"]
                engine = instance["Engine"]

                encrypted_in_transit = None
                if engine.startswith("mysql"):  # Apply check only for MySQL-based instances
                    parameter_group_name = instance["DBParameterGroups"][0]["DBParameterGroupName"]
                    parameters = parameter_group_details.get(parameter_group_name, {}).get("Parameters", [])

                    for param in parameters:
                        if param["ParameterName"] == "require_secure_transport":
                            encrypted_in_transit = param["ParameterValue"] == "1"
                            break

                if (findings["rds_mysql_encryption_in_transit"]["status"] == ResourceComplianceStatusEnum.PASS.value
                        and encrypted_in_transit is not None and not encrypted_in_transit):
                    findings["rds_mysql_encryption_in_transit"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["rds_mysql_encryption_in_transit"]["details"].append({
                    "db_instance_id": db_instance_id,
                    "engine": engine,
                    "region": region,
                    "compliance": encrypted_in_transit if encrypted_in_transit is not None else True
                })

        return findings

    def remediate(self):
        pass
