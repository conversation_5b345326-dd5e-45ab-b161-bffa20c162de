from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, EKSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.eks.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "private_node_groups": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.PRIVATE_NODE_GROUPS.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = (cached.get("clusters") or {}).get("clusters", [])
            node_groups = cached.get("node_groups") or {}
            node_group_details = cached.get("node_group_details") or {}

            for cluster_arn in clusters:
                cluster_name = cluster_arn.split("/")[-1]
                cluster_node_groups = node_groups.get(cluster_name, [])
                cluster_node_group_details = node_group_details.get(cluster_name, {})

                if not cluster_node_groups:
                    # No node groups found - this might be a Fargate-only cluster
                    findings["private_node_groups"]["details"].append({
                        "cluster_name": cluster_name,
                        "region": region,
                        "node_group_name": "N/A",
                        "remote_access_configured": True,
                        "private_subnets_only": True,
                        "compliance": True,
                        "note": "No node groups found - possibly Fargate-only cluster"
                    })
                    continue

                for node_group_name in cluster_node_groups:
                    node_group = cluster_node_group_details.get(node_group_name, {}).get("nodegroup", {})
                    
                    # Check if node group has public IP addresses
                    remote_access = node_group.get("remoteAccess", {})
                    has_public_ip = remote_access.get("ec2SshKey") is not None and not remote_access.get("sourceSecurityGroups")
                    
                    # Check subnet configuration (private vs public)
                    subnets = node_group.get("subnets", [])
                    private_subnets_only = True  # Assume private unless proven otherwise
                    
                    # Check if remote access is properly configured
                    remote_access_configured = True
                    if remote_access.get("ec2SshKey"):
                        # If SSH key is configured, ensure source security groups are specified
                        source_security_groups = remote_access.get("sourceSecurityGroups", [])
                        remote_access_configured = len(source_security_groups) > 0
                    
                    # Overall compliance check
                    is_compliant = not has_public_ip and remote_access_configured and private_subnets_only
                    
                    if findings["private_node_groups"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_compliant:
                        findings["private_node_groups"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["private_node_groups"]["details"].append({
                        "cluster_name": cluster_name,
                        "region": region,
                        "node_group_name": node_group_name,
                        "remote_access_configured": remote_access_configured,
                        "private_subnets_only": private_subnets_only,
                        "subnet_count": len(subnets),
                        "ami_type": node_group.get("amiType", "Unknown"),
                        "instance_types": node_group.get("instanceTypes", []),
                        "compliance": is_compliant
                    })

        return findings

    def remediate(self):
        pass
