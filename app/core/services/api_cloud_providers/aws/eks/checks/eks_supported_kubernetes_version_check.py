from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, EKSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.eks.data_fetch import read_cached_region_data, get_supported_kubernetes_versions


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "supported_kubernetes_version": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.SUPPORTED_KUBERNETES_VERSION.value,
                "severity": SeverityEnum.HIGH.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = (cached.get("clusters") or {}).get("clusters", [])
            cluster_details = cached.get("cluster_details") or {}

            # Get dynamically supported Kubernetes versions for this region
            supported_versions = get_supported_kubernetes_versions(cached)

            for cluster_arn in clusters:
                cluster_name = cluster_arn.split("/")[-1]
                cluster_detail = cluster_details.get(cluster_name, {}).get("cluster", {})

                kubernetes_version = cluster_detail.get("version", "")
                is_supported_version = kubernetes_version in supported_versions

                if findings["supported_kubernetes_version"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_supported_version:
                    findings["supported_kubernetes_version"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["supported_kubernetes_version"]["details"].append({
                    "cluster_name": cluster_name,
                    "region": region,
                    "kubernetes_version": kubernetes_version,
                    "compliance": is_supported_version
                })

        return findings

    def remediate(self):
        pass
