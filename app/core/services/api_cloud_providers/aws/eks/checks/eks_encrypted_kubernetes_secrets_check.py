from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, EKSChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.eks.data_fetch import read_cached_region_data


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "encrypted_kubernetes_secrets": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": EKSChecksDescriptionEnum.ENCRYPTED_KUBERNETES_SECRETS.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            clusters = (cached.get("clusters") or {}).get("clusters", [])
            cluster_details = cached.get("cluster_details") or {}

            for cluster_arn in clusters:
                cluster_name = cluster_arn.split("/")[-1]
                cluster_detail = cluster_details.get(cluster_name, {}).get("cluster", {})
                
                encryption_config = cluster_detail.get("encryptionConfig", [])
                
                has_secrets_encryption = any(
                    "secrets" in provider.get("resources", [])
                    for config in encryption_config
                    for provider in config.get("provider", {}).values()
                )
                
                if findings["encrypted_kubernetes_secrets"]["status"] == ResourceComplianceStatusEnum.PASS.value and not has_secrets_encryption:
                    findings["encrypted_kubernetes_secrets"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["encrypted_kubernetes_secrets"]["details"].append({
                    "cluster_name": cluster_name,
                    "region": region,
                    "encryption_config": encryption_config,
                    "has_secrets_encryption": has_secrets_encryption,
                    "compliance": has_secrets_encryption
                })

        return findings

    def remediate(self):
        pass
