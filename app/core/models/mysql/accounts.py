from .helper import fetch_row, insert, fetch_rows, delete, insert_many
from ...models import sql_scripts


__all__ = ['check_account_exists', 'add_account', 'get_accounts', 'delete_account', 
           'add_user_account', 'get_account_detail', 'get_account_recent_scans', 'get_accounts_with_findings',
           'get_accounts_by_workspace']


async def check_account_exists(conn_pool, generic_key_str, params):
    """Check if account already exists within the specified workspace"""
    return await fetch_row(conn_pool,
                           sql_stmt=sql_scripts['check_account_exists'].replace("#generic_key_str#", generic_key_str),
                           params=params)


async def add_account(conn_pool, payload):
    """Add account to accounts table"""
    result = await insert(conn_pool, sql_stmt=sql_scripts['add_account'], params=payload)
    return result['id']


async def get_accounts(conn_pool, cloud_provider_id, workspace_id):
    """Get all accounts"""
    return await fetch_rows(conn_pool, sql_stmt=sql_scripts['get_accounts'],
                            params={"cloud_provider_id": cloud_provider_id, "workspace_id": workspace_id})


async def delete_account(conn_pool, account_id):
    """Delete account"""
    return await delete(conn_pool, sql_stmt=sql_scripts['delete_account'], params={"account_id": account_id})


async def add_user_account(conn_pool, user_id, account_id):
    """Add user account"""
    return await insert(conn_pool, sql_stmt=sql_scripts['add_user_account'],
                        params={"user_id": user_id, "account_id": account_id})


async def get_account_detail(conn_pool, account_id, user_id):
    """Get detailed information about a specific account"""
    return await fetch_row(
        conn_pool, 
        sql_stmt=sql_scripts['get_account_detail'],
        params={"account_id": account_id, "user_id": user_id}
    )


async def get_account_recent_scans(conn_pool, account_id, limit=5):
    """Get recent scans for an account"""
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_account_recent_scans'],
        params={"account_id": account_id, "limit": limit}
    )


async def get_accounts_with_findings(conn_pool, cloud_provider_id, workspace_id):
    """Get all accounts with their last scan date and finding counts in a single query"""
    return await fetch_rows(
        conn_pool,
        sql_stmt=sql_scripts['get_accounts_with_findings'],
        params={"cloud_provider_id": cloud_provider_id, "workspace_id": workspace_id}
    )


async def get_accounts_by_workspace(conn_pool, workspace_id):
    """Get all accounts in a workspace"""
    return await fetch_rows(
        conn_pool, 
        sql_stmt=sql_scripts['get_accounts_by_workspace'],
        params={"workspace_id": workspace_id}
    )
